[
    {'id': '00027eef-b08c-4673-9618-d63adf636e8f', 'source': 'openai_agent', 'models_usage': RequestUsage(prompt_tokens=242, completion_tokens=13), 'metadata': {}, 'created_at': datetime.datetime(2025,
        8,
        4,
        11,
        48,
        21,
        974787, tzinfo=datetime.timezone.utc), 'content': [FunctionCall(id='call_KDgHS3yOMuujkamC9eCprl8j', arguments='{
                "x": 100
            }', name='log')
        ], 'type': 'ToolCallRequestEvent'
    },
    {'id': 'bf8b8c31-68c3-431d-bafe-eb0ada7f8107', 'source': 'openai_agent', 'models_usage': None, 'metadata': {}, 'created_at': datetime.datetime(2025,
        8,
        4,
        11,
        48,
        22,
        4641, tzinfo=datetime.timezone.utc), 'content': [FunctionExecutionResult(content='2.0', name='log', call_id='call_KDgHS3yOMuujkamC9eCprl8j', is_error=False)
        ], 'type': 'ToolCallExecutionEvent'
    },
    {'id': '1d1b83da-4883-42eb-b3a1-087c5571f8ac', 'source': 'openai_agent', 'models_usage': RequestUsage(prompt_tokens=63, completion_tokens=25), 'metadata': {}, 'created_at': datetime.datetime(2025,
        8,
        4,
        11,
        48,
        23,
        191526, tzinfo=datetime.timezone.utc), 'content': 'I have tested the MCP function with a value of 100 and the result is 2.0. \n\nTERMINATE', 'type': 'TextMessage'
    }
]